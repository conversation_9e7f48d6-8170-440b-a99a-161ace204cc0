import pandas as pd
import numpy as np
import os

def calculate_ABCD(filepath):
    """从 Excel 文件中读取数据并计算 A、B、C、D 系数。"""
    try:
        df = pd.read_excel(filepath)
        if not all(col in df.columns for col in ['x', 'y', 'r']):
            raise ValueError("Excel 文件必须包含 'x', 'y', 'r' 列。")
        return df
    except FileNotFoundError:
        print(f"文件未找到：{filepath}")
        return None
    except ValueError as e:
        print(f"数据错误：{e}")
        return None
    except Exception as e:
        print(f"发生未知错误：{e}")
        return None

def main():
    """主函数，用于获取文件路径，计算并输出结果。"""
    while True:
        filepath = input("请输入 Excel 文件的路径（直接回车退出）：")
        if not filepath:
            break

        df = calculate_ABCD(filepath)
        if df is None:
            continue  # 继续循环，等待用户输入新的路径

        print("\n读取的数据：\n")
        print(df)

        while True: # 循环直到用户输入有效确认
            confirm = input("\n确认数据无误并继续计算？（y/n）：")
            if confirm.lower() == 'y':
                break
            elif confirm.lower() == 'n':
                break
            else:
                print("无效输入，请重新输入 'y' 或 'n'")

        if confirm.lower() == 'n':
            continue #如果用户选择n，则重新输入文件路径

        x = df['x'].to_numpy()
        y = df['y'].to_numpy()
        r = df['r'].to_numpy()
        n = len(x)

        x_bar = np.mean(x)
        y_bar = np.mean(y)
        r_bar = np.mean(r)

        A = 0.5 * np.sum(x**2 - y**2 - x_bar * x + y_bar * y)
        B = -0.5 * np.sum(2 * x * y - x_bar * y - y_bar * x)
        C = np.sum(r_bar * y - r * y)
        D = np.sum(r_bar * x - r * x)

        print("\n计算结果：")
        print(f"A: {A}")
        print(f"B: {B}")
        print(f"C: {C}")
        print(f"D: {D}")
        print(f"x平均: {x_bar}")
        print(f"y平均: {y_bar}")
        print(f"r平均: {r_bar}")
        input("按任意键退出程序...")
        break # 计算完成后退出循环
if __name__ == "__main__":
    main()