import tkinter as tk
from tkinter import ttk
from math import sin, cos, pi
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from decimal import Decimal, getcontext, ROUND_HALF_UP

class NewtonSolver:
    def __init__(self):
        self.theta_n = Decimal(0.0)  # 初始猜测值
        self.A = Decimal(0.0)
        self.B = Decimal(0.0)
        self.C = Decimal(0.0)
        self.D = Decimal(0.0)
        self.precision = 10  # 默认有效数字为 10 位
        getcontext().prec = self.precision  # 设置全局精度

    def set_precision(self, precision):
        """设置有效数字"""
        self.precision = precision
        getcontext().prec = precision
        print(f"有效数字已设置为：{precision} 位")

    def format_decimal(self, value):
        """将浮点数或数字转换为 Decimal"""
        return Decimal(value)

    def format_output(self, value):
        """格式化计算结果，保留指定有效数字"""
        quantize_pattern = Decimal(f"1.{'0' * (self.precision - 1)}")
        return value.quantize(quantize_pattern, rounding=ROUND_HALF_UP)

    def f(self, theta):
        """计算目标函数值"""
        theta = self.format_decimal(theta)
        result = (self.A * Decimal(sin(2 * float(theta))) +
                  self.B * Decimal(cos(2 * float(theta))) +
                  self.C * Decimal(sin(float(theta))) +
                  self.D * Decimal(cos(float(theta))))
        return self.format_output(result)

    def f_for_plot(self, theta):
        """绘图时的目标函数值，固定有效数字为 6"""
        original_precision = getcontext().prec
        getcontext().prec = 6  # 设置临时有效数字为 6
        theta = self.format_decimal(theta)
        result = (self.A * Decimal(sin(2 * float(theta))) +
                  self.B * Decimal(cos(2 * float(theta))) +
                  self.C * Decimal(sin(float(theta))) +
                  self.D * Decimal(cos(float(theta))))
        getcontext().prec = original_precision  # 恢复原始精度
        return result

    def f_prime(self, theta):
        """计算目标函数的一阶导数"""
        theta = self.format_decimal(theta)
        result = (2 * self.A * Decimal(cos(2 * float(theta))) -
                  2 * self.B * Decimal(sin(2 * float(theta))) +
                  self.C * Decimal(cos(float(theta))) -
                  self.D * Decimal(sin(float(theta))))
        return self.format_output(result)

    def iterate(self):
        """执行一次牛顿迭代"""
        f_theta_n = self.f(self.theta_n)
        f_prime_theta_n = self.f_prime(self.theta_n)

        if f_prime_theta_n == 0:
            raise ValueError("导数为零，无法继续迭代")

        theta_n1 = self.theta_n - f_theta_n / f_prime_theta_n
        delta_theta = abs(theta_n1 - self.theta_n)
        f_theta_n1 = abs(self.f(theta_n1))

        self.theta_n = theta_n1
        return (self.format_output(theta_n1),
                self.format_output(delta_theta),
                self.format_output(f_theta_n1))


def validate_input(entry, default_value=0.0):
    """验证输入框的值，返回浮点数或默认值"""
    try:
        return float(entry.get())
    except ValueError:
        return default_value


def update_precision(event=None):
    """更新有效数字"""
    precision = validate_input(entry_precision, default_value=10)
    if 1 <= precision <= 64:
        solver.set_precision(int(precision))
        result_label.config(text=f"有效数字已设置为 {precision} 位")
        entry_A.focus_set()
        entry_A.select_range(0, tk.END)
        plot_function()
    else:
        result_label.config(text="请输入 1 到 64 之间的整数！")


def update_parameters(event=None, next_entry=None):
    """更新参数值并重新绘图"""
    solver.A = solver.format_decimal(validate_input(entry_A))
    solver.B = solver.format_decimal(validate_input(entry_B))
    solver.C = solver.format_decimal(validate_input(entry_C))
    solver.D = solver.format_decimal(validate_input(entry_D))
    plot_function()

    if next_entry:  # 聚焦并选中下一个输入框
        next_entry.focus_set()
        next_entry.select_range(0, tk.END)


def calculate():
    """执行一次牛顿迭代"""
    try:
        solver.theta_n = solver.format_decimal(validate_input(entry_theta_0)) #set initial theta
        theta_n1, delta_theta, f_theta_n1 = solver.iterate()
        result_label.config(
            text=f"θ_(n+1) = {theta_n1}, |θ_(n+1) - θ_n| = {delta_theta}, |f(θ_(n+1))| = {f_theta_n1}"
        )
        entry_theta_0.delete(0, tk.END)
        entry_theta_0.insert(0, f"{theta_n1}")
        plot_function(theta_n1)
    except ValueError as e:
        result_label.config(text=str(e))


def plot_function(theta_n1=None):
    """绘制目标函数曲线"""
    # 使用固定有效数字（6 位）进行绘图
    theta_values = [i * pi / 200 - pi / 2 for i in range(400)]
    f_values = [float(solver.f_for_plot(theta)) for theta in theta_values]

    ax.clear()
    ax.plot(theta_values, f_values, 'b-', label='f(θ)')
    ax.axhline(0, color='black', linewidth=0.8, linestyle='--')

    if theta_n1 is not None:
        ax.plot(float(theta_n1), float(solver.f(theta_n1)), 'ro', label='Current Iteration Point') #修改标签

    ax.set_xlim(-pi / 2, pi / 2)
    ax.set_xlabel('θ')
    ax.set_ylabel('f(θ)')
    ax.set_title('Function') #修改标题
    ax.legend()
    canvas.draw()


# Create main window
root = tk.Tk()
root.title("牛顿迭代法求解器")
root.geometry("800x600") # Set window size
root.resizable(False, False) # Prevent resizing
style = ttk.Style()
style.theme_use('clam') # Use a nicer theme


# Create input frames for better organization
parameter_frame = ttk.LabelFrame(root, text="参数设置", padding="10")
parameter_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

precision_frame = ttk.Frame(parameter_frame)
precision_frame.grid(row=0, column=0, padx=5, pady=5, sticky="w")
ttk.Label(precision_frame, text="有效数字:").grid(row=0, column=0, sticky="w")
entry_precision = ttk.Entry(precision_frame)
entry_precision.insert(0, "10")
entry_precision.grid(row=0, column=1, padx=5)
entry_precision.bind("<Return>", update_precision)

parameter_entries = [("A", 0), ("B", 0), ("C", 0), ("D", 0)]
for i, (param, default) in enumerate(parameter_entries):
    ttk.Label(parameter_frame, text=f"{param}:").grid(row=i+1, column=0, sticky="w", padx=5, pady=2)
    entry = ttk.Entry(parameter_frame)
    entry.insert(0, str(default))
    entry.grid(row=i+1, column=1, padx=5, pady=2)
    entry.bind("<Return>", lambda e, next_entry=parameter_entries[(i+1)%len(parameter_entries)][1]: update_parameters(event=e, next_entry=next_entry))


initial_theta_frame = ttk.Frame(parameter_frame)
initial_theta_frame.grid(row=5, column=0, columnspan=2, padx=5, pady=5, sticky="w")
ttk.Label(initial_theta_frame, text="初始θ:").grid(row=0, column=0, sticky="w")
entry_theta_0 = ttk.Entry(initial_theta_frame)
entry_theta_0.insert(0, "0")
entry_theta_0.grid(row=0, column=1, padx=5)
entry_theta_0.bind("<Return>", lambda e: calculate())


# Create buttons
button_frame = ttk.Frame(root)
button_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
calculate_button = ttk.Button(button_frame, text="计算", command=calculate)
calculate_button.grid(row=0, column=0, padx=5)
clear_button = ttk.Button(button_frame, text="清除", command=lambda: [entry.delete(0, tk.END) for entry in [entry_precision, entry_theta_0, *[parameter_frame.grid_slaves(row=i+1, column=1)[0] for i in range(4)]]])
clear_button.grid(row=0, column=1, padx=5)


# Result label
result_label = ttk.Label(root, text="", wraplength=700) # Added wraplength for long outputs
result_label.grid(row=2, column=0, padx=10, pady=10, sticky="w")

# Plot area
fig, ax = plt.subplots(figsize=(5, 4))
canvas = FigureCanvasTkAgg(fig, master=root)
canvas.get_tk_widget().grid(row=3, column=0, padx=10, pady=10, sticky="nsew")

# Global variables for easy access to entry widgets.  Could be improved with a more structured approach.
entry_A = parameter_frame.grid_slaves(row=1, column=1)[0]
entry_B = parameter_frame.grid_slaves(row=2, column=1)[0]
entry_C = parameter_frame.grid_slaves(row=3, column=1)[0]
entry_D = parameter_frame.grid_slaves(row=4, column=1)[0]

solver = NewtonSolver()

# Initial plot
plot_function()

root.columnconfigure(0, weight=1) # Make the column expand to fill the window
root.rowconfigure(3, weight=1) # Make the plot area expand to fill the window

root.mainloop()