import tkinter as tk
from tkinter import ttk
from math import sin, cos, pi
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import decimal
from decimal import Decimal, getcontext, ROUND_HALF_UP

class NewtonSolver:
    def __init__(self):
        self.theta_n = Decimal(0.0)  # 初始猜测值
        self.A = Decimal(0.0)
        self.B = Decimal(0.0)
        self.C = Decimal(0.0)
        self.D = Decimal(0.0)
        self.precision = 10  # 默认有效数字为 10 位
        self.max_precision = 12  # 最大有效数字限制
        getcontext().prec = self.precision  # 设置全局精度
        # 设置异常处理，避免InvalidOperation错误
        getcontext().traps[decimal.InvalidOperation] = 0

    def set_precision(self, precision):
        """设置有效数字，限制最大为12位"""
        # 限制精度范围
        precision = max(1, min(precision, self.max_precision))
        self.precision = precision
        getcontext().prec = precision
        print(f"有效数字已设置为：{precision} 位（最大限制：{self.max_precision}位）")

    def format_decimal(self, value):
        """将浮点数或数字转换为 Decimal，带溢出保护"""
        try:
            if isinstance(value, str):
                decimal_value = Decimal(value)
            else:
                decimal_value = Decimal(str(value))

            # 检查数值范围，避免过大的数值
            if abs(decimal_value) > Decimal('1e10'):
                # 对于过大的数值，进行范围限制
                if decimal_value > 0:
                    decimal_value = Decimal('1e10')
                else:
                    decimal_value = Decimal('-1e10')

            return decimal_value
        except (decimal.InvalidOperation, ValueError, OverflowError):
            # 如果转换失败，返回0
            return Decimal('0')

    def format_output(self, value):
        """格式化计算结果，保留指定有效数字，带溢出保护"""
        try:
            # 检查是否为有限数
            if not value.is_finite():
                return Decimal('0')  # 将无穷大或NaN转换为0

            # 对于过大或过小的数值，使用科学记数法
            if abs(value) > Decimal('1e8') or (abs(value) < Decimal('1e-8') and value != 0):
                return value.normalize()

            # 正常情况下的量化
            quantize_pattern = Decimal(f"1.{'0' * (self.precision - 1)}")
            return value.quantize(quantize_pattern, rounding=ROUND_HALF_UP)
        except (decimal.InvalidOperation, OverflowError):
            # 如果格式化失败，返回规范化的值
            try:
                return value.normalize()
            except:
                return Decimal('0')

    def normalize_angle(self, theta_float):
        """将角度规范化到合理范围，避免数值问题"""
        # 将角度限制在 [-10π, 10π] 范围内
        max_angle = 10 * pi
        if abs(theta_float) > max_angle:
            theta_float = theta_float % (2 * pi)
            # 将结果映射到 [-π, π] 范围
            if theta_float > pi:
                theta_float -= 2 * pi
            elif theta_float < -pi:
                theta_float += 2 * pi
        return theta_float

    def f(self, theta):
        """计算目标函数值，带溢出保护"""
        try:
            theta = self.format_decimal(theta)
            theta_float = float(theta)

            # 规范化角度以避免数值问题
            theta_float = self.normalize_angle(theta_float)

            # 安全计算三角函数值
            sin_2theta = self.format_decimal(sin(2 * theta_float))
            cos_2theta = self.format_decimal(cos(2 * theta_float))
            sin_theta = self.format_decimal(sin(theta_float))
            cos_theta = self.format_decimal(cos(theta_float))

            # 计算结果，每一步都进行溢出检查
            term1 = self.A * sin_2theta
            term2 = self.B * cos_2theta
            term3 = self.C * sin_theta
            term4 = self.D * cos_theta

            result = term1 + term2 + term3 + term4

            # 检查结果是否在合理范围内
            if abs(result) > Decimal('1e12'):
                raise ValueError("函数值过大，可能存在数值不稳定")

            return self.format_output(result)
        except Exception as e:
            raise ValueError(f"函数计算失败: {str(e)}")

    def f_for_plot(self, theta):
        """绘图时的目标函数值，固定有效数字为 6"""
        original_precision = getcontext().prec
        getcontext().prec = 6  # 设置临时有效数字为 6
        theta = self.format_decimal(theta)
        result = (self.A * Decimal(sin(2 * float(theta))) +
                  self.B * Decimal(cos(2 * float(theta))) +
                  self.C * Decimal(sin(float(theta))) +
                  self.D * Decimal(cos(float(theta))))
        getcontext().prec = original_precision  # 恢复原始精度
        return result

    def f_prime(self, theta):
        """计算目标函数的一阶导数，带溢出保护"""
        try:
            theta = self.format_decimal(theta)
            theta_float = float(theta)

            # 规范化角度以避免数值问题
            theta_float = self.normalize_angle(theta_float)

            # 安全计算三角函数值
            cos_2theta = self.format_decimal(cos(2 * theta_float))
            sin_2theta = self.format_decimal(sin(2 * theta_float))
            cos_theta = self.format_decimal(cos(theta_float))
            sin_theta = self.format_decimal(sin(theta_float))

            # 计算导数，每一步都进行溢出检查
            term1 = 2 * self.A * cos_2theta
            term2 = -2 * self.B * sin_2theta
            term3 = self.C * cos_theta
            term4 = -self.D * sin_theta

            result = term1 + term2 + term3 + term4

            # 检查结果是否在合理范围内
            if abs(result) > Decimal('1e12'):
                raise ValueError("导数值过大，可能存在数值不稳定")

            return self.format_output(result)
        except Exception as e:
            raise ValueError(f"导数计算失败: {str(e)}")

    def iterate(self):
        """执行一次牛顿迭代，带步长限制和溢出保护"""
        try:
            f_theta_n = self.f(self.theta_n)
            f_prime_theta_n = self.f_prime(self.theta_n)

            # 检查导数是否为零或接近零（调整阈值更合理）
            min_derivative = Decimal('1e-10')
            if abs(f_prime_theta_n) < min_derivative:
                raise ValueError(f"导数过小 ({f_prime_theta_n})，无法继续迭代。建议尝试不同的初始值。")

            # 计算牛顿步长
            newton_step = f_theta_n / f_prime_theta_n

            # 限制步长以防止发散（自适应步长控制）
            max_step = Decimal('1.0')  # 最大步长限制
            if abs(newton_step) > max_step:
                # 使用缩放因子来限制步长
                scale_factor = max_step / abs(newton_step)
                newton_step = newton_step * scale_factor

            theta_n1 = self.theta_n - newton_step

            # 将角度规范化到合理范围
            theta_n1_float = float(theta_n1)
            theta_n1_float = self.normalize_angle(theta_n1_float)
            theta_n1 = self.format_decimal(theta_n1_float)

            delta_theta = abs(theta_n1 - self.theta_n)
            f_theta_n1 = abs(self.f(theta_n1))

            # 检查结果是否有效
            if not (theta_n1.is_finite() and delta_theta.is_finite() and f_theta_n1.is_finite()):
                raise ValueError("计算结果包含无穷大或NaN值。建议尝试不同的初始值。")

            # 检查是否出现数值不稳定
            if abs(delta_theta) > Decimal('10'):
                raise ValueError("步长过大，可能出现数值不稳定。建议尝试不同的初始值。")

            self.theta_n = theta_n1
            return (self.format_output(theta_n1),
                    self.format_output(delta_theta),
                    self.format_output(f_theta_n1))
        except Exception as e:
            raise ValueError(f"迭代计算失败: {str(e)}")


def validate_input(entry, default_value=0.0):
    """验证输入框的值，返回浮点数或默认值，带范围限制"""
    try:
        value = float(entry.get())
        # 限制参数值的范围，避免过大的数值
        max_value = 1e6  # 最大值限制
        min_value = -1e6  # 最小值限制

        if value > max_value:
            return max_value
        elif value < min_value:
            return min_value
        else:
            return value
    except ValueError:
        return default_value


def update_precision(event=None):
    """更新有效数字，限制最大为12位"""
    precision = validate_input(entry_precision, default_value=10)
    if 1 <= precision <= 12:
        solver.set_precision(int(precision))
        result_label.config(text=f"有效数字已设置为 {precision} 位")
        entry_A.focus_set()
        entry_A.select_range(0, tk.END)
        plot_function()
    else:
        result_label.config(text="请输入 1 到 12 之间的整数！（限制最大精度为12位）")


def update_parameters(event=None, next_entry=None):
    """更新参数值并重新绘图"""
    solver.A = solver.format_decimal(validate_input(entry_A))
    solver.B = solver.format_decimal(validate_input(entry_B))
    solver.C = solver.format_decimal(validate_input(entry_C))
    solver.D = solver.format_decimal(validate_input(entry_D))
    plot_function()

    if next_entry:  # 聚焦并选中下一个输入框
        next_entry.focus_set()
        next_entry.select_range(0, tk.END)


def suggest_better_initial_values():
    """建议更好的初始值"""
    suggestions = []
    # 测试一些常用的初始值
    test_values = [0, 0.1, 0.5, 1.0, -0.1, -0.5, -1.0, pi/6, pi/4, pi/3, -pi/6, -pi/4, -pi/3]

    for theta in test_values:
        try:
            f_val = abs(float(solver.f(theta)))
            f_prime_val = abs(float(solver.f_prime(theta)))
            # 选择函数值较小且导数不为零的点
            if f_prime_val > 1e-8 and f_val < 1e6:
                suggestions.append((theta, f_val))
        except:
            continue

    # 按函数值排序，返回最好的3个建议
    suggestions.sort(key=lambda x: x[1])
    return suggestions[:3]

def calculate():
    """执行一次牛顿迭代，带智能错误处理"""
    try:
        # 限制初始值范围
        initial_theta = validate_input(entry_theta_0)
        if abs(initial_theta) > 100:  # 限制初始值范围
            initial_theta = initial_theta % (2 * pi)
            if initial_theta > pi:
                initial_theta -= 2 * pi
            elif initial_theta < -pi:
                initial_theta += 2 * pi

        solver.theta_n = solver.format_decimal(initial_theta)
        theta_n1, delta_theta, f_theta_n1 = solver.iterate()

        result_label.config(
            text=f"θ_(n+1) = {theta_n1}, |θ_(n+1) - θ_n| = {delta_theta}, |f(θ_(n+1))| = {f_theta_n1}"
        )
        entry_theta_0.delete(0, tk.END)
        entry_theta_0.insert(0, f"{theta_n1}")
        plot_function(theta_n1)

    except ValueError as e:
        error_msg = str(e)
        # 如果是数值问题，提供建议的初始值
        if any(keyword in error_msg.lower() for keyword in ["导数过小", "不稳定", "无穷大", "nan"]):
            suggestions = suggest_better_initial_values()
            if suggestions:
                suggestion_text = f"{error_msg}\n\n建议尝试以下初始值: "
                for i, (theta, f_val) in enumerate(suggestions):
                    suggestion_text += f"θ≈{theta:.3f}"
                    if i < len(suggestions) - 1:
                        suggestion_text += ", "
                result_label.config(text=suggestion_text)
            else:
                result_label.config(text=f"{error_msg}\n建议尝试更小的参数值或不同的初始值。")
        else:
            result_label.config(text=error_msg)


def plot_function(theta_n1=None):
    """绘制目标函数曲线"""
    # 使用固定有效数字（6 位）进行绘图
    theta_values = [i * pi / 200 - pi / 2 for i in range(400)]
    f_values = [float(solver.f_for_plot(theta)) for theta in theta_values]

    ax.clear()
    ax.plot(theta_values, f_values, 'b-', label='f(θ)')
    ax.axhline(0, color='black', linewidth=0.8, linestyle='--')

    if theta_n1 is not None:
        ax.plot(float(theta_n1), float(solver.f(theta_n1)), 'ro', label='Current Iteration Point') #修改标签

    ax.set_xlim(-pi / 2, pi / 2)
    ax.set_xlabel('θ')
    ax.set_ylabel('f(θ)')
    ax.set_title('Function') #修改标题
    ax.legend()
    canvas.draw()


# Create main window
root = tk.Tk()
root.title("牛顿迭代法求解器")
root.geometry("800x600") # Set window size
root.resizable(False, False) # Prevent resizing
style = ttk.Style()
style.theme_use('clam') # Use a nicer theme


# Create input frames for better organization
parameter_frame = ttk.LabelFrame(root, text="参数设置", padding="10")
parameter_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

precision_frame = ttk.Frame(parameter_frame)
precision_frame.grid(row=0, column=0, padx=5, pady=5, sticky="w")
ttk.Label(precision_frame, text="有效数字(1-12):").grid(row=0, column=0, sticky="w")
entry_precision = ttk.Entry(precision_frame, width=8)
entry_precision.insert(0, "10")
entry_precision.grid(row=0, column=1, padx=5)
entry_precision.bind("<Return>", update_precision)

# Create parameter entry widgets
parameter_entries = []
for i, (param, default) in enumerate([("A", 0), ("B", 0), ("C", 0), ("D", 0)]):
    ttk.Label(parameter_frame, text=f"{param}:").grid(row=i+1, column=0, sticky="w", padx=5, pady=2)
    entry = ttk.Entry(parameter_frame)
    entry.insert(0, str(default))
    entry.grid(row=i+1, column=1, padx=5, pady=2)
    parameter_entries.append(entry)

# Bind Return key events after all entries are created
def bind_parameter_events():
    for i, entry in enumerate(parameter_entries):
        if i < 3:  # For A, B, C entries
            entry.bind("<Return>", lambda e, idx=i: update_parameters(event=e, next_entry=parameter_entries[idx+1]))
        else:  # For D entry, focus on initial theta
            entry.bind("<Return>", lambda e: [update_parameters(event=e), entry_theta_0.focus_set(), entry_theta_0.select_range(0, tk.END)])

bind_parameter_events()


initial_theta_frame = ttk.Frame(parameter_frame)
initial_theta_frame.grid(row=5, column=0, columnspan=2, padx=5, pady=5, sticky="w")
ttk.Label(initial_theta_frame, text="初始θ:").grid(row=0, column=0, sticky="w")
entry_theta_0 = ttk.Entry(initial_theta_frame)
entry_theta_0.insert(0, "0")
entry_theta_0.grid(row=0, column=1, padx=5)
entry_theta_0.bind("<Return>", lambda e: calculate())


# Create buttons
button_frame = ttk.Frame(root)
button_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
calculate_button = ttk.Button(button_frame, text="计算", command=calculate)
calculate_button.grid(row=0, column=0, padx=5)
clear_button = ttk.Button(button_frame, text="清除", command=lambda: [entry.delete(0, tk.END) for entry in [entry_precision, entry_theta_0, *parameter_entries]])
clear_button.grid(row=0, column=1, padx=5)


# Result label
result_label = ttk.Label(root, text="", wraplength=700) # Added wraplength for long outputs
result_label.grid(row=2, column=0, padx=10, pady=10, sticky="w")

# Plot area
fig, ax = plt.subplots(figsize=(5, 4))
canvas = FigureCanvasTkAgg(fig, master=root)
canvas.get_tk_widget().grid(row=3, column=0, padx=10, pady=10, sticky="nsew")

# Global variables for easy access to entry widgets
entry_A = parameter_entries[0]
entry_B = parameter_entries[1]
entry_C = parameter_entries[2]
entry_D = parameter_entries[3]

solver = NewtonSolver()

# Initial plot
plot_function()

root.columnconfigure(0, weight=1) # Make the column expand to fill the window
root.rowconfigure(3, weight=1) # Make the plot area expand to fill the window

root.mainloop()