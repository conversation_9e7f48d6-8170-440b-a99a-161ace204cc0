
import math

while True:
    try:
        x, y, r, theta = map(float, input("请输入x, y, r, theta (用逗号分隔，theta为弧度制): ").split(','))
        print(f"您输入的值为: x = {x}, y = {y}, r = {r}, theta = {theta}")
        confirmation = input("确认这些值是否正确?(y/n): ")
        if confirmation.lower() == 'y':
            break
        else:
            print("请重新输入。")
    except ValueError:
        print("输入无效，请使用逗号分隔数值。")


b = r - x * math.sin(theta) + y * math.cos(theta)

m = math.tan(theta)
c = b / math.cos(theta)

print("\n计算结果:")
print(f"b = {b}")
print(f"斜率 m = {m}")
print(f"截距 c = {c}")

theta_degrees = math.degrees(theta)
print("\n角度制 theta =", theta_degrees)
