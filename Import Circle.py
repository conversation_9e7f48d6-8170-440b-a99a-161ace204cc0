import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import hsv_to_rgb


class CircleApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Circle and Line Drawer")

        # 设置窗口初始大小为屏幕的2/3
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = int(screen_width * 2 / 3)
        window_height = int(screen_height * 2 / 3)
        self.root.geometry(f"{window_width}x{window_height}")

        # 创建 PanedWindow（分割窗口）
        self.paned_window = tk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)

        # 左侧控制区域
        self.control_frame = tk.Frame(self.paned_window, width=200)
        self.paned_window.add(self.control_frame)

        # 右侧绘图区域
        self.plot_frame = tk.Frame(self.paned_window)
        self.paned_window.add(self.plot_frame)

        # 初始化绘图区域
        self.setup_plot()

        # 创建控制区域内容
        self.create_controls()

        # 初始化属性
        self.circles = []  # 存储圆和圆心的列表
        self.lines = []    # 存储直线的列表
        self.axis_lines = []  # 存储坐标轴的线
        self.show_axes = False  # 坐标轴是否显示
        self.free_view_enabled = False
        self.press = None  # 鼠标按下的位置
        self.original_colors = []  # 存储圆的原始颜色
        self.is_colorful = False  # 是否为多彩视图模式

        # 初始视图范围
        self.initial_xlim = self.ax.get_xlim()
        self.initial_ylim = self.ax.get_ylim()

        # 绑定鼠标事件
        self.bind_mouse_events()

    def setup_plot(self):
        """初始化右侧绘图区域"""
        self.fig, self.ax = plt.subplots()
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.ax.set_aspect('equal', 'box')
        self.ax.set_xlim(-10, 10)
        self.ax.set_ylim(-10, 10)

    def create_controls(self):
        """创建左侧控制区域内容"""
        row = 0
        tk.Label(self.control_frame, text="Controls", font=("Arial", 14, "bold")).grid(row=row, column=0, columnspan=2, pady=10)
        row += 1

        # 圆相关输入框和按钮
        tk.Label(self.control_frame, text="Center (x, y):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.center_entry = tk.Entry(self.control_frame)
        self.center_entry.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        tk.Label(self.control_frame, text="Radius:").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.radius_entry = tk.Entry(self.control_frame)
        self.radius_entry.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        tk.Button(self.control_frame, text="Add Circle", command=self.add_circle).grid(row=row, column=0, pady=5)
        tk.Button(self.control_frame, text="Delete Circle", command=self.delete_circle).grid(row=row, column=1, pady=5)
        row += 1
        tk.Button(self.control_frame, text="Import Circles from Excel", command=self.import_circles_from_excel).grid(row=row, column=0, columnspan=2, pady=5)
        row += 1

        # 直线相关输入框和按钮
        tk.Label(self.control_frame, text="Line Controls", font=("Arial", 12, "bold")).grid(row=row, column=0, columnspan=2, pady=10)
        row += 1
        tk.Label(self.control_frame, text="Angle (degrees):").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.angle_entry = tk.Entry(self.control_frame)
        self.angle_entry.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        tk.Label(self.control_frame, text="Intercept:").grid(row=row, column=0, sticky=tk.E, padx=5, pady=5)
        self.intercept_entry = tk.Entry(self.control_frame)
        self.intercept_entry.grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        tk.Button(self.control_frame, text="Add Line", command=self.add_line).grid(row=row, column=0, pady=5)
        tk.Button(self.control_frame, text="Delete Line", command=self.delete_line).grid(row=row, column=1, pady=5)
        row += 1

        # 视图相关按钮
        tk.Label(self.control_frame, text="View Controls", font=("Arial", 12, "bold")).grid(row=row, column=0, columnspan=2, pady=10)
        row += 1
        tk.Button(self.control_frame, text="自由视图", command=self.enable_free_view).grid(row=row, column=0, pady=5)
        tk.Button(self.control_frame, text="重置视图", command=self.reset_view).grid(row=row, column=1, pady=5)
        row += 1
        tk.Button(self.control_frame, text="Toggle Axes", command=self.toggle_axes).grid(row=row, column=0, columnspan=2, pady=10)
        row += 1

        # 多彩视图按钮
        tk.Button(self.control_frame, text="Toggle Colorful View", command=self.toggle_colorful_view).grid(row=row, column=0, columnspan=2, pady=10)

    def bind_mouse_events(self):
        """绑定鼠标交互事件"""
        self.canvas.mpl_connect("scroll_event", self.zoom)
        self.canvas.mpl_connect("button_press_event", self.on_press)
        self.canvas.mpl_connect("button_release_event", self.on_release)
        self.canvas.mpl_connect("motion_notify_event", self.on_motion)

    def generate_random_colors(self, n):
        """生成 n 个随机鲜艳且区分度高的颜色（排除红色、黑色、白色）"""
        hues = np.linspace(0, 1, n, endpoint=False)  # 等间隔生成色调
        np.random.shuffle(hues)  # 打乱色调顺序
        colors = [hsv_to_rgb([hue, 1, 1]) for hue in hues]  # 转换为 RGB 颜色
        return colors

    def toggle_colorful_view(self):
        """切换多彩视图模式"""
        if self.is_colorful:
            # 恢复原始颜色
            for (circle, _), color in zip(self.circles, self.original_colors):
                circle.set_edgecolor(color)
            self.is_colorful = False
        else:
            # 保存原始颜色
            self.original_colors = [circle.get_edgecolor() for circle, _ in self.circles]

            # 分配随机颜色
            random_colors = self.generate_random_colors(len(self.circles))
            for (circle, _), color in zip(self.circles, random_colors):
                circle.set_edgecolor(color)
            self.is_colorful = True

        self.ax.figure.canvas.draw()

    def toggle_axes(self):
        """显示或隐藏坐标轴"""
        if self.show_axes:
            for line in self.axis_lines:
                line.remove()
            self.axis_lines = []
            self.show_axes = False
        else:
            xlim, ylim = self.ax.get_xlim(), self.ax.get_ylim()
            x_axis = self.ax.annotate('', xy=(xlim[1], 0), xytext=(xlim[0], 0),
                                      arrowprops=dict(facecolor='black', arrowstyle='->'))
            y_axis = self.ax.annotate('', xy=(0, ylim[1]), xytext=(0, ylim[0]),
                                      arrowprops=dict(facecolor='black', arrowstyle='->'))
            self.axis_lines.extend([x_axis, y_axis])
            self.show_axes = True
        self.ax.figure.canvas.draw()

    def add_circle(self):
        """添加圆"""
        try:
            center = tuple(map(float, self.center_entry.get().split(',')))
            radius = float(self.radius_entry.get())
            if radius <= 0:
                raise ValueError("Radius must be positive.")
            circle = plt.Circle(center, radius, color='blue', fill=False)
            center_marker = self.ax.scatter(center[0], center[1], color='black', s=25)
            self.circles.append((circle, center_marker))
            self.ax.add_artist(circle)
            self.update_plot()
        except ValueError:
            messagebox.showerror("Invalid Input", "Please enter valid numbers for center (x, y) and radius.")

    def delete_circle(self):
        """删除最近添加的圆"""
        if self.circles:
            circle, center_marker = self.circles.pop()
            circle.remove()
            center_marker.remove()
            self.update_plot()

    def import_circles_from_excel(self):
        """从 Excel 文件中导入多个圆"""
        file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx")])
        if not file_path:
            return

        try:
            data = pd.read_excel(file_path)
            if not all(col in data.columns for col in ["x", "y", "r"]):
                raise ValueError("The Excel file must contain columns: 'x', 'y', and 'r'.")
            for _, row in data.iterrows():
                x, y, r = row["x"], row["y"], row["r"]
                if r <= 0:
                    continue
                circle = plt.Circle((x, y), r, color='blue', fill=False)
                center_marker = self.ax.scatter(x, y, color='black', s=25)
                self.circles.append((circle, center_marker))
                self.ax.add_artist(circle)
            self.update_plot()
        except Exception as e:
            messagebox.showerror("Import Error", f"Failed to import circles: {e}")

    def add_line(self):
        """添加直线"""
        try:
            angle = float(self.angle_entry.get())
            intercept = float(self.intercept_entry.get())
            slope = np.tan(np.radians(angle))
            x = np.array(self.ax.get_xlim())
            y = slope * x + intercept
            line, = self.ax.plot(x, y, color='red')
            self.lines.append(line)
            self.update_plot()
        except ValueError:
            messagebox.showerror("Invalid Input", "Please enter valid numbers for angle and intercept.")

    def delete_line(self):
        """删除最近添加的直线"""
        if self.lines:
            line = self.lines.pop()
            line.remove()
            self.update_plot()

    def enable_free_view(self):
        """启用自由视图模式"""
        self.free_view_enabled = True

    def reset_view(self):
        """重置视图到初始状态"""
        self.free_view_enabled = False
        self.ax.set_xlim(self.initial_xlim)
        self.ax.set_ylim(self.initial_ylim)
        self.update_plot()

    def zoom(self, event):
        """缩放图像"""
        if not self.free_view_enabled:
            return

        base_scale = 1.1
        scale_factor = 1 / base_scale if event.button == 'up' else base_scale
        xdata, ydata = event.xdata, event.ydata
        cur_xlim = self.ax.get_xlim()
        cur_ylim = self.ax.get_ylim()

        new_width = (cur_xlim[1] - cur_xlim[0]) * scale_factor
        new_height = (cur_ylim[1] - cur_ylim[0]) * scale_factor
        relx = (cur_xlim[1] - xdata) / (cur_xlim[1] - cur_xlim[0])
        rely = (cur_ylim[1] - ydata) / (cur_ylim[1] - cur_ylim[0])

        self.ax.set_xlim([xdata - new_width * (1 - relx), xdata + new_width * relx])
        self.ax.set_ylim([ydata - new_height * (1 - rely), ydata + new_height * rely])
        self.ax.figure.canvas.draw()

    def on_press(self, event):
        """记录鼠标按下位置"""
        if self.free_view_enabled and event.button == 1:
            self.press = event.x, event.y

    def on_release(self, event):
        """释放鼠标按键"""
        self.press = None

    def on_motion(self, event):
        """拖动视图"""
        if self.press is None or not self.free_view_enabled:
            return

        xpress, ypress = self.press
        dx = (event.x - xpress)/36
        dy = (event.y - ypress)/36

        cur_xlim = self.ax.get_xlim()
        cur_ylim = self.ax.get_ylim()

        scale_x = (self.initial_xlim[1] - self.initial_xlim[0]) / (cur_xlim[1] - cur_xlim[0])
        scale_y = (self.initial_ylim[1] - self.initial_ylim[0]) / (cur_ylim[1] - cur_ylim[0])

        dx /= scale_x
        dy /= scale_y

        self.ax.set_xlim([cur_xlim[0] - dx, cur_xlim[1] - dx])
        self.ax.set_ylim([cur_ylim[0] - dy, cur_ylim[1] - dy])
        self.press = event.x, event.y
        self.ax.figure.canvas.draw()

    def update_plot(self):
        """更新图像"""
        self.ax.figure.canvas.draw()


if __name__ == "__main__":
    root = tk.Tk()
    app = CircleApp(root)
    root.mainloop()